<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />
    <div class="py-4 flex items-center">
      <img src="@/assets/icons/report_details.png" alt="report-icon" class="w-8 h-8 mr-2" />
      <h3 class="text-2xl font-semibold uppercase">Report Details</h3>
    </div>
    <div>
      <div class="bg-gray-50 border border-gray-100 rounded">
        <div class="flex px-2 py-2 items-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 48 48">
            <path fill="currentColor" fill-rule="evenodd"
              d="M31 10a1 1 0 1 0 0 2v7h-3v-7a1 1 0 1 0 0-2h-8a1 1 0 1 0 0 2v7h-3v-7a1 1 0 1 0 0-2H9a1 1 0 1 0 0 2v7H7v-3H5v3H4v2h1v13H4v2h1v2h2v-2h34v2h2v-2h1v-2h-1V21h1v-2h-1v-3h-2v3h-2v-7a1 1 0 1 0 0-2zm6 9v-7h-4v7zm-4 2h4v5h-4zm-2 0v8a4 4 0 0 0 8 0v-8h2v13H7V21h2v8a4 4 0 0 0 8 0v-8h3v8a4 4 0 0 0 8 0v-8zm-5-2v-7h-4v7zm-11 0v-7h-4v7zm-4 2h4v2h-4z"
              clip-rule="evenodd" />
          </svg>
          <p class="px-2 text-base font-medium uppercase">
            {{ route.query.test }} (total tests count: {{ route.query.count }})
          </p>
        </div>

        <div class="flex px-2 py-2 items-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 512 512">
            <rect width="416" height="384" x="48" y="80" fill="none" stroke="currentColor" stroke-linejoin="round"
              stroke-width="32" rx="48" />
            <circle cx="296" cy="232" r="24" fill="currentColor" />
            <circle cx="376" cy="232" r="24" fill="currentColor" />
            <circle cx="296" cy="312" r="24" fill="currentColor" />
            <circle cx="376" cy="312" r="24" fill="currentColor" />
            <circle cx="136" cy="312" r="24" fill="currentColor" />
            <circle cx="216" cy="312" r="24" fill="currentColor" />
            <circle cx="136" cy="392" r="24" fill="currentColor" />
            <circle cx="216" cy="392" r="24" fill="currentColor" />
            <circle cx="296" cy="392" r="24" fill="currentColor" />
            <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"
              d="M128 48v32m256-32v32" />
            <path fill="none" stroke="currentColor" stroke-linejoin="round" stroke-width="32" d="M464 160H48" />
          </svg>
          <p class="px-2 text-base font-medium">
            <span class="font-medium text-black uppercase">Report period:</span>
            {{
              requiredDate(
                route.query.origin?.toString() as ReportDrilldownOrigin,
                route.query.from?.toString(),
                route.query.to?.toString(),
                route.query.dateRange?.toString()
              )
            }}
          </p>
        </div>
      </div>
      <div class="w-full flex items-center justify-between mt-3">
        <div>
          <ReportsDetailFilters @tableFields="populateTableFields" @specimenFields="populateSpecimenFields"
            @locationField="populateLocationFields" @onClearFilters="onFiltersCleared" />
        </div>
        <div class="flex items-center space-x-3">
          <excel class="btn btn-default" :header="[
            `${route.query.origin
              ?.toString()
              .toUpperCase()} - ${route.query.type
                ?.toString()
                .replaceAll('-', ' ')
                .toUpperCase()} `,
            `FOR PERIOD ${requiredDate(
              route.query.origin?.toString() as ReportDrilldownOrigin,
              route.query.from?.toString(),
              route.query.to?.toString(),
              route.query.dateRange?.toString()
            )}`,
            facility.details.name,
            facility.details.address,
            facility.details.phone,
            `${route.query.test?.toString()?.toUpperCase()}`]" :data="exportData" worksheet="report-work-sheet" :name="slugifyReportName([
              `${route.query.origin?.toString()}`,
              `${route.query.type?.toString()}`,
              `${route.query.test?.toString()}`,
              `.xls`,
            ])
              ">
            <CoreExportButton text="Export Excel" />
          </excel>
        </div>
      </div>
      <div class="mt-5">
        <CoreDatatable :headers="computedHeaders" :data="computedReportData" :loading="loading"
          :rows-per-items="rowsPerItems" :searchField="searchField" :searchValue="searchValue"
          :serverItemsLength="serverItemsLength" :serverOptions="serverOptions" :sortBy="sortBy" :sortType="sortType"
          @update="update"></CoreDatatable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type {
  DrillDownReportItem,
  Header,
  Page,
  ReportDrilldownOrigin,
  Request,
  Response,
  TableField,
} from "@/types";
import moment from "moment";
import type { ServerOptions, SortType } from "vue3-easy-data-table";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import { useFacilityStore } from "@/store/facility";
import packageJson from "@/package.json";
import { log } from "console";

definePageMeta({
  layout: "dashboard",
});

const route = useRoute();
const { getDisplayName } = useDisplayService();

useHead({
  title: `${capitalize(
    route.query.type?.toString()?.toUpperCase().replaceAll("-", " ") ?? ""
  )} Drilldown - ${packageJson.name.toUpperCase()}`,
});

const reportData = ref([]);
const loading = ref<boolean>(false);
const searchField = ref<string>("");
const cookie = useCookie("token");
const facility = useFacilityStore();
const router = useRouter();
const searchValue = ref<string>("name");
const serverItemsLength = ref<number>(0);
const rowsPerItems = ref<number[]>([10, 25, 50, 100, 500, 1000])
const sortBy = ref<string>("patient_name");
const sortType = ref<string>("asc");
const specimenFields = ref<string[]>([]);
const laboratoryLocationFields = ref<string[]>([]);
const serverOptions = ref<ServerOptions>({
  page: 1,
  rowsPerPage: 10,
  sortBy: "name",
});

const HEADERS = ref<Header>([
  { text: "SN", value: "sn", sortable: false },
  { text: "PATIENT NO", value: "patient_id", sortable: true },
  { text: "PATIENT NAME", value: "patient_name", sortable: true },
  { text: "ACCESSION NO", value: "accession_number", sortable: true },
  { text: "TEST", value: "test_type_name", sortable: true },
  { text: "DATE REGISTERED", value: "date_registered", sortable: true },
]);

const computedHeaders = ref<Header>(HEADERS.value);
const filterTableFields = ref<string[]>([])

const pages = <Page>[
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Reports",
    link: "#",
  },
];

const requiredDate = (
  origin: ReportDrilldownOrigin,
  from?: string,
  to?: string,
  dateRange?: string
): string => {
  const month: string = moment(dateRange).format("MMMM");
  const year: string = moment(dateRange).format("yyyy");
  const originMap: Record<string, string> = {
    "aggregate": `${moment(from).format(DATE_FORMAT)} - ${moment(to).format(
      DATE_FORMAT
    )}`,
    "culture": `${month}, ${year}`,
    "moh": `${dateRange}`,
  };
  return originMap[origin];
};

const computedReportData = computed(() => {
  return reportData.value
    .map((report: any, index: number) => {
      const completedObject =
        report.completed_by && report.completed_by.username
          ? report.completed_by.username
          : "";
      const resultObject =
        report.results && report.results.length > 0
          ? report.results.find((result: any) => result.value !== null)
          : {};
      const results =
        report.results && report.results.length > 0
          ? report.results
            .filter(
              (result: { name: string }) => result.name !== "Lab Tech. Name:"
            )
            .map(
              (result: { name: string; value: string }) =>
                `${result.name}: ${result.value}`
            )
            .join(", ")
          : "";
      return {
        sn: index + 1,
        patient_id: report.client.patient_no,
        patient_name: `${report.client.first_name} ${report.client.last_name}`,
        accession_number: report.accession_number,
        test_type_name: getDisplayName(report.test_type_name, report.test_type_preferred_name),
        specimen_type: getDisplayName(report.specimen_type, report.specimen_preferred_name),
        result: results,
        result_date: moment(resultObject.result_date).format(DATE_FORMAT),
        completed: completedObject,
        requested_by: report.requested_by,
        tracking_number: report.tracking_number,
        lab_location: report.lab_location.name,
        rejection_reason: report.rejection_reason,
        date_registered: moment(report.created_date).format(DATE_FORMAT),
      };
    }).filter((item) =>
      specimenFields.value.length === 0 || specimenFields.value.includes(getDisplayName(item.specimen_type, item.specimen_preferred_name))
    ).filter((item) =>
      laboratoryLocationFields.value.length === 0 || laboratoryLocationFields.value.includes(item.lab_location)
    );
});

const exportData = computed(() => {
  return computedReportData.value.map((item: DrillDownReportItem) => {
    let baseObject: Record<any, any> = {
      SN: item.sn,
      "PATIENT NO": item.patient_id,
      "PATIENT NAME": item.patient_name,
      "ACCESSION NO": item.accession_number,
      "TEST": item.test_type_name,
      "SPECIMEN": item.specimen_type,
      "TEST RESULT": item.result.replace(/,/g, "\n"),
      "LAB LOCATION": item.lab_location,
      "RESULT DATE": item.result_date,
      "COMPLETED BY": item.completed,
      "REQUESTED BY": item.requested_by,
      "REJECTION EASON": item.rejection_reason,
      "TRACKING NUMBER": item.tracking_number,
      "DATE REGISTERED": item.date_registered,
    };

    const filters = [
      ...(filterTableFields.value.length > 0
        ? [([key]: [string, any]) =>
          filterTableFields.value
            .map(field => field.toUpperCase())
            .includes(key.toUpperCase()) ||
          HEADERS.value.some(header => header.text === key.toUpperCase())]
        : []),

      ...(specimenFields.value.length > 0
        ? [([key]: [string, any]) => key.toUpperCase() === "SPECIMEN"]
        : []),

      ...(laboratoryLocationFields.value.length > 0
        ? [([key]: [string, any]) => key.toUpperCase() === "LAB LOCATION"]
        : []),

      ([key]: [string, any]) =>
        computedHeaders.value
          .map(header => header.text.toUpperCase())
          .includes(key.toUpperCase()),
    ];

    return Object.fromEntries(
      Object.entries(baseObject).filter(entry =>
        filters.some(filterFn => filterFn(entry))
      )
    );
  });
});


const ROUTES = ref([
  {
    origin: "aggregate",
    path: `${endpoints.aggregateDrilldown}?associated_ids=${route.params.associated_id}`,
  },
]);

const getDetails = async (): Promise<void> => {
  loading.value = true;
  const endpoint = ROUTES.value.find((route) => route.origin === "aggregate");
  if (!endpoint) {
    console.error("Endpoint not found");
    return;
  }
  const request: Request = {
    route: `${endpoint.path}&page=${serverOptions.value.page}&per_page=${serverOptions.value.rowsPerPage}`,
    method: "GET",
    token: `${cookie.value}`,
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;
  if (data.value) {
    reportData.value = data.value.data;
    serverItemsLength.value = data.value.meta.total_count;
    loading.value = false;
    if (route.query.type?.includes('reject')) {
      const position = HEADERS.value.findIndex(header => header.value === "test_type_name") + 1;
      const rejectionHeader = { text: "REJECTION REASON", value: "rejection_reason", sortable: true };
      HEADERS.value.splice(position, 0, rejectionHeader);
    }
  }

  if (error.value) {
    console.error(error.value);
    loading.value = false;
  }
};

const update = (value: {
  page: number;
  rowsPerPage: number;
  sortBy?: string | string[];
  sortType?: SortType | SortType[];
}): void => {
  if (typeof value === "object") {
    serverOptions.value = value;
    const currentRoute = router.currentRoute.value;
    const newQuery = {
      ...currentRoute.query,
      page: value.page,
      rowsPerPage: value.rowsPerPage,
    };
    router.replace({ query: newQuery });
    getDetails();
  }
};

const onFiltersCleared = (tableFields: TableField[]): void => {
  specimenFields.value = [];
  laboratoryLocationFields.value = [];
  dePopulateTableFields(tableFields);
  computedHeaders.value = HEADERS.value
    .filter((header) => !header.text.includes("Specimen"))
    .filter((header) => !header.text.includes("Lab Location"));
};

const populateTableFields = (fields: TableField[]) => {
  computedHeaders.value = [...HEADERS.value];
  filterTableFields.value = [];
  if (fields.length > 0) {
    const currentHeaders = new Set(computedHeaders.value.map(h => h.text));
    fields.forEach((field: TableField) => {
      if (!currentHeaders.has(field.property)) {
        computedHeaders.value.push({
          text: field.property,
          value: field.table_value,
          sortable: true,
        });
        filterTableFields.value.push(field.property);
      }
    });
    const fieldProperties = new Set(fields.map(f => f.property));
    computedHeaders.value = computedHeaders.value.filter(header =>
      fieldProperties.has(header.text) || HEADERS.value.some(h => h.text === header.text)
    );
  }
};


const dePopulateTableFields = (fields: TableField[]) => {
  fields.forEach(
    (field: { value: boolean; property: string; table_value: string }) => {
      if (!field.value) {
        computedHeaders.value = computedHeaders.value.filter(
          (header) => header.text !== field.property
        );
      }
    }
  );
  if (specimenFields.value.length === 0 && laboratoryLocationFields.value.length === 0) {
    computedHeaders.value = [...HEADERS.value];
  }
};


const populateSpecimenFields = (fields: string): void => {
  const fieldsArray = fields
    .split(",")
    .filter((field: string) => field.trim() !== "");
  specimenFields.value = fieldsArray;

  if (fieldsArray.length > 0) {
    addSpecimenHeader({ text: "Specimen", value: "specimen_type", sortable: true });
  } else {
    removeSpecimenHeader();
  }

  if (specimenFields.value.length === 0 && laboratoryLocationFields.value.length === 0) {
    HEADERS.value = [...HEADERS.value];
  }
};

const populateLocationFields = (fields: string): void => {
  const fieldsArray = fields
    .split(",")
    .filter((field: string) => field.trim() !== "");
  laboratoryLocationFields.value = fieldsArray;

  if (fieldsArray.length > 0) {
    addLocationHeader({ text: "Lab Location", value: "lab_location", sortable: true });
  } else {
    removeLocationHeader();
  }
  if (specimenFields.value.length === 0 && laboratoryLocationFields.value.length === 0) {
    HEADERS.value = [...HEADERS.value];
  }
};


const addSpecimenHeader = (header: any): void => {
  if (!computedHeaders.value.some((h) => h.text === "Specimen")) {
    computedHeaders.value.push(header);
  }
};

const removeSpecimenHeader = (): void => {
  computedHeaders.value = computedHeaders.value.filter((header) => header.text !== "Specimen");
};

const addLocationHeader = (header: any): void => {
  if (!computedHeaders.value.some((h) => h.text === "Lab Location")) {
    computedHeaders.value.push(header);
  }
};

const removeLocationHeader = (): void => {
  computedHeaders.value = computedHeaders.value.filter(
    (header) => header.text !== "Lab Location"
  );
};

const slugifyReportName = (fields: string[]): string => {
  const name = fields.join("-").toLowerCase();
  return name.replace(/ /g, "-");
};

onMounted(() => {
  getDetails();
});
</script>

<style scoped lang="scss"></style>
