<template>
    <div class="px-5 py-5">

        <CoreBreadcrumb :pages="pages" />

        <div class="flex items-center py-5">
            <img src="@/assets/icons/report.png" alt="report-icon" class="w-8 h-8 mr-2" />
            <h3 class="text-2xl font-semibold uppercase">{{ title }}</h3>
        </div>

        <div class="flex justify-between items-center">
            <FormKit type="form" submit-label="Update" @submit="checkDropdownValues() && generateReport()"
                :actions="false" #default="{ value }" id="submitForm">
                <div class="w-full flex items-center space-x-3">
                    <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
                        <FunnelIcon class="w-5 h-5 mr-2" />
                        Filter By Date Range
                        <div class="w-72 ml-2">
                            <datepicker @cleared="isCleared" required position="left"
                                placeholder="select start & end date" :range="true" input-class-name="datepicker"
                                v-model="dateRange" format="dd/MM/yyyy" :maxDate="new Date()"/>
                        </div>
                    </div>
                    <div class="w-48">
                        <CoreDropdown :items="departments" v-model="selectedDepartment" />
                    </div>
                    <div class="w-48">
                        <CoreDropdown :items="timeUnits" v-model="unitSelected" />
                    </div>
                    <div class="w-48">
                        <CoreActionButton type="submit" color="primary" text="Generate Report" :icon="refreshIcon"
                            :click="(() => { })" :loading="loading" />
                    </div>
                </div>
            </FormKit>
            <excel class="btn btn-default"
                :header="[`TURN AROUND TIME REPORT ${startDate} - ${endDate}`, facility.details.name, facility.details.address, facility.details.phone]"
                :data="exportData" worksheet="report-work-sheet"
                :name="`turn_around_time_${startDate}_to_${endDate}.xls`">
                <CoreExportButton text="Export Excel" />
            </excel>
        </div>

        <div class="border rounded mt-10" id="print-container">

            <div class="flex items-center justify-between px-5 py-5 border-b">
                <div class="flex flex-col space-y-2">
                    <img src="@/assets/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
                    <h3 class="text-xl font-semibold">
                        TURN AROUND TIME REPORT
                    </h3>
                </div>
                <ReportsAddress />
            </div>

            <div class="m-3">
                <h3 class="font-semibold mb-2">Tests Performed Period: <span class="text-normal font-normal">
                        {{ startDate !== "" ? moment(startDate).format(DATE_FORMAT) : "" }} - {{ endDate !== "" ?
                            moment(endDate).format(DATE_FORMAT) : "" }}
                    </span></h3>
            </div>

            <div v-if="statistics.length > 0 && !loading">
                <table class="w-full rounded">
                    <thead>
                        <tr class="border-t bg-gray-50 border-b">
                            <th class="text-left px-2 py-2 border-r uppercase">Test Type</th>
                            <th class="text-left px-2 py-2 border-r uppercase">Target TAT
                                ({{ unitSelected.name }})</th>
                            <th class="text-left px-2 py-2 uppercase">Average TAT
                                ({{ unitSelected.name }})</th>
                            <th class="text-left px-2 py-2 uppercase">Total Tests</th>
                            <th class="text-left px-2 py-2 uppercase">Tests Within Normal TAT</th>
                            <th class="text-left px-2 py-2 uppercase">Percentage of tests within Normal TAT</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(report, index) in statistics" v-bind:key="index"
                            :class="{ 'bg-white': index % 2 === 0, 'bg-gray-50': index % 2 !== 0 }">
                            <td class="text-left px-2 py-2 border-r border-b">{{ report.test_type }}</td>
                            <td class="text-left px-2 py-2 border-r border-b">{{ report.turn_around_time }}</td>
                            <td class="text-left px-2 py-2 border-b border-r">{{ report.average }}</td>
                            <td class="text-left px-2 py-2 border-b border-r">{{ report.total_tests }}</td>
                            <td class="text-left px-2 py-2 border-b">{{ report.tests_within_normal_tat }}</td>
                            <td class="text-left px-2 py-2 border-b">{{ report.percentage_within_normal_tat }}%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div v-if="statistics.length == 0 && !loading"
                class="w-full flex flex-col items-center justify-center space-y-2 py-10">
                <img src="@/assets/images/page.png" alt="page-icon" class="object-cover w-20 h-20" />
                <p>Data not found, please generate report</p>
            </div>
            <ReportsLoader :condition="loading" :cancelReportGeneration="() => cancelRequest()" />
        </div>

        <div class="bg-gray-50 rounded mt-5 border" v-if="statistics.length > 0 && !loading">
            <div class="px-5 py-5 flex flex-col items-center">
                <h3 class="text-lg font-medium">Average Turn Around Time</h3>
                <p class="mt-1">From: {{ startDate !== "" ? moment(startDate).format(DATE_FORMAT) : "" }} - To: {{
                    endDate !== "" ? moment(endDate).format(DATE_FORMAT) : "" }}</p>
            </div>
            <div style="height: 400px;" v-if="statistics.length > 0">
                <ChartsBar :chart-data="chartData"></ChartsBar>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

import { ArrowPathIcon as refreshIcon, FunnelIcon } from '@heroicons/vue/24/solid/index.js';
import moment from 'moment';
import { endpoints } from '@/services/endpoints';
import { useFacilityStore } from '@/store/facility';
import type { Department, DropdownItem, Page, Request, Response } from '@/types';
import Package from '@/package.json'

definePageMeta({
    layout: 'dashboard',
    middleware: ['reports']
});
useHead({
    title: `${Package.name.toUpperCase()} - Turn Around Time Reports`
});

const title = "Turn Around Time Report";
const pages = ref<Page>([
    {
        name: "Home",
        link: "/home"
    },
    {
        name: "Reports",
        link: "#"
    },
    {
        name: "Aggregate Reports",
        link: "#"
    }
])
const departments = ref<Array<Department>>([])
const selectedDepartment = ref<Department>({ name: 'select department', id: 0 })
const { loading, executeCancellableRequest, cancelRequest } = useCancellableRequest();
const timeUnits = ref<Array<DropdownItem>>([
    {
        name: "Minutes"
    },
    {
        name: "Hours"
    },
    {
        name: "Days"
    },
    {
        name: "Weeks"
    }
]);
const unitSelected = ref<DropdownItem>({ name: "select unit" })
const statistics = ref<Array<any>>([])
const facility = useFacilityStore();
const { $toast, $metadata } = useNuxtApp();
const cookie = useCookie('token');
const chartData = ref<any>({})
const dateRange: Ref<Array<any>> = ref<Array<any>>(
    new Array(
        '', ''
    )
);
const isCleared = (): void => {
    dateRange.value = new Array(
        '', ''
    )
};

const startDate = computed(() => {
    return dateRange.value[0] ? moment(dateRange.value[0]).format('YYYY-MM-DD') : '';
});
const endDate = computed(() => {
    return dateRange.value[1] ? moment(dateRange.value[1]).format('YYYY-MM-DD') : '';
});

interface Statistic {
    test_type: string;
    turn_around_time: string;
    average: number;
    total_tests: number;
    tests_within_normal_tat: number;
    percentage_within_normal_tat: number;
}
const exportData = computed(() => {
    return statistics.value.length > 0
        ? statistics.value.map((statistic: Statistic) => ({
            "TEST TYPE": statistic.test_type,
            "UNITS": unitSelected.value.name,
            "TURN AROUND TIME": statistic.turn_around_time,
            "AVERAGE": statistic.average,
            "TOTAL TESTS": statistic.total_tests,
            "TESTS WITHIN NORMAL TAT": statistic.tests_within_normal_tat,
            "PERCENTAGE OF TESTS WITHIN NORMAL TAT": `${statistic.percentage_within_normal_tat}%`
        }))
        : [];
});

async function generateReport(): Promise<void> {
    loading.value = true;
    chartData.value = {}
    let queryParams = `from=${startDate.value}&to=${endDate.value}&department=${selectedDepartment.value.id}&unit=${unitSelected.value.name.toLowerCase()}`;
    const request: Request = {
        route: `${endpoints.aggregateReports}turn_around_time?${queryParams}`,
        method: 'GET',
        token: `${cookie.value}`
    };
    const { data, error, pending }: Response = await executeCancellableRequest(request);
    loading.value = pending;
    if (data.value) {
        loading.value = false;
        statistics.value = data.value.data;
        chartData.value = {
            labels: data.value.data.map((item: {
                test_type: string;
            }) => item.test_type),
            datasets: [
                {
                    label: 'Turn Around Time',
                    backgroundColor: '#030712',
                    data: data.value.data.map((item: {
                        turn_around_time: number;
                    }) => item.turn_around_time)
                },
                {
                    label: 'Average Turn Around Time',
                    backgroundColor: '#0284c7',
                    data: data.value.data.map((item: {
                        average: string;
                    }) => Number(item.average))
                }
            ]
        };
        data.value.data.length > 0 ?
            ($toast.success('Report data generated successfully'))
            :
            ($toast.warning(`No data found in period ${moment(startDate.value).format(DATE_FORMAT)} - ${moment(endDate.value).format(DATE_FORMAT)}`))
    }
    if (error.value) {
        loading.value = false;
        console.error(error.value);
        $toast.error(ERROR_MESSAGE);
    }
}

function checkDropdownValues(): boolean {
    const isInvalidSelection = selectedDepartment.value.name === 'select department' || unitSelected.value.name === 'select unit';
    if (isInvalidSelection) {
        $toast.warning('Please select a department and unit');
    }
    return !isInvalidSelection;
}

watch((unitSelected), (a, b) => {
    if (a !== b) {
        statistics.value = [];
    }
});

watch((selectedDepartment), (a, b) => {
    if (a !== b) {
        statistics.value = [];
    }
});

watch((dateRange), (a, b) => {
    if (a !== b) {
        statistics.value = [];
    }
});

onMounted(() => {
    loading.value = false;
    departments.value = $metadata.departments;
    const allDepartmentExists = departments.value.some(
        (department: Department) => department.name == "All"
    );
    if (!allDepartmentExists) {
        departments.value.push({ id: 0, name: "All" });
    }
    departments.value.sort((a: Department, b: Department) => {
        const nameA = a.name.toUpperCase();
        const nameB = b.name.toUpperCase();
        if (nameA < nameB) {
            return -1;
        }
        if (nameA > nameB) {
            return 1;
        }
        return 0;
    });
})
</script>

<style></style>