import instrumentService from './instrument.service';
import specimensLifespanService from './specimens-lifespan.service';
import surveillanceService from './surveillance.service';

export const endpoints = {
	global: `global`,
	login: `auth/login`,
	logout: `auth/logout`,
	refreshToken: `auth/refresh_token`,
	departments: `departments`,
	locations: `lab_locations`,
	testTypes: `test_types`,
	viewTestType: `test_types`,
	testTypesIndicators: `test_indicator_types`,
	specimens: `specimen`,
	drugs: `drugs`,
	organisms: `organisms`,
	testPanels: `test_panels`,
	rejectionReasons: `status_reasons`,

	/**
	* User Management
	*/
	users: `users`,

	roles: `roles`,

	privileges: `privileges`,

	/***
	* Client Management
	*/

	client: {
		search: `clients/search_dde`,
	},
	clients: `clients`,

	/***
	* Instrument Management
	*/
	...instrumentService,

	/***
	* surveillance Management
	*/
	...surveillanceService,

	/***
	* surveillance Management
	*/
	...specimensLifespanService,

	/**
	* Lab Configurations
	*/
	visitTypes: `encounter_types`,

	facility: `facilities`,

	sections: `facility_sections`,

	/**
	* Fetch Test Results
	*/
	fetchResults: `interfacer/fetch_results`,

	/**
	* Test statuses
	*/
	testStatus: `test_statuses`,

	addTestOrder: `orders/add_test_to_order`,

	specimenTestTypes: `specimen/test_types`,

	/**
	* Results available
	*/
	resultsAvailable: `interfacer/result_available`,
	/*
	* Tests Module
	*/

	tests: `tests`,

	orderStatus: `order_statuses`,

	nlimsTestSearch: `orders/search_order_from_nlims_by_tracking_number`,
	/*
	* Update Tests results
	*/
	updateResults: `test_results`,

	cultureObservations: `culture_observations`,

	drugSusceptibility: `culture_observations/drug_susceptibility_test_results`,

	/*
	* Update Tests results
	*/
	authoriseTest: `interfacer`,
	/*
	* Printers
	*/
	printers: `printers`,

	printOut: `printout/patient_report`,

	generalPrint: `printout/general_report`,

	printOutZebra: `printout/patient_zebra_report`,

	/***
	 * Reports
	 */
	reportIndicators: `moh_reports/report_indicators`,

	mohReport: `moh_reports/`,

	dailyReports: `/reports/daily_reports/`,

	aggregateReports: `/reports/aggregate/`,

	mergeOrder: `orders/merge_order_from_nlims`,
	/**
	 * Analytics
	 */
	analytics: `analytics`,

	aggregateDrilldown: `reports/aggregate/drilldown`,
	settings: `/global/app_settings`,
	whonet: 'whonet/report',
	addOrderToEncounter: `orders/add_order_to_encounter`,
	processCrossMatch: `/post_crossmatch_processes`,
	preferences: "global/preferences",
	userPreferences: "user_preferences",


	/**
	 * test catalog
	 */
	checkTestCatalogVersion: 'check_for_new_test_catalog_version',
	syncTestCatalog: 'sync_test_catalog',
	testCatalogVersions: 'test_catalog_versions'
};
