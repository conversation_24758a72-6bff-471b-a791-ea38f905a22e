import { FunctionalComponent } from "vue";
export * from './lab-sections';

export type AgeRange = {
  "0-5": number;
  "5-14": number;
  "14-120": number;
};

export type Network = {
  ip: string;
  port: number;
};

/***
 * Generic Types
 */
export type Request = {
  route: string;
  method:
  | "get"
  | "GET"
  | "HEAD"
  | "PATCH"
  | "POST"
  | "PUT"
  | "DELETE"
  | "CONNECT"
  | "OPTIONS"
  | "TRACE"
  | "head"
  | "patch"
  | "post"
  | "put"
  | "delete"
  | "connect"
  | "options";
  body?: any;
  token?: string;
};

export type Response = {
  data: { value: any };
  error: { value: any };
  pending: any;
};

export type DropdownItem = {
  id?: number;
  name: string;
};

export type ButtonType = "submit" | "button" | "reset";

export type Header = Array<{
  text: string;
  value: string;
  sortable: boolean;
  html?: boolean;
}>;
/** user payload object types */

export type UserPayloadInterface = {
  username: string;
  password: string;
  department: string;
};

export type Permission = {
  id: number;
  name: string;
};

export type AccountUser = {
  id: number;
  is_active: boolean;
  first_name: string;
  last_name: string;
  username: string;
  sex: string;
};

export type Priviledge = {
  id: number;
  name: string;
  display_name: string;
};

export type Role = {
  id: number;
  name: string;
  priviledges: Priviledge[];
};

export type Preference = {
  id: number;
  name: string;
  value: string;
}

export type User = {
  id: number;
  username: string;
  first_name: string;
  middle_name: string;
  last_name: string;
  date_of_birth: string;
  departments: Array<any>;
  roles: Array<any>;
  permissions?: Permission[];
  preferences: Preference[];
};

/** breadcrumb page data types */
export type Page = Array<{
  name: String;
  link: String;
}>;

export type Route = Array<{
  name: string;
  href: string;
  icon?: FunctionalComponent;
}>;

export type Department = {
  id?: number;
  name: string;
};

export type Location = {
  id?: number;
  name: string;
};

/**
 * Test catalog types
 */
export type Specimen = {
  id: number;
  name: string;
  preferred_name: string;
};

interface ExpectedTurnAroundTime {
  id?: number;
  test_type_id?: number;
  value: string;
  unit: string;
  voided?: number;
  voided_by?: null | number;
  voided_reason?: null | string;
  voided_date?: null | string;
  creator?: number;
  created_date?: string;
  updated_date?: string;
  updated_by?: number;
}

export type TestType = {
  id?: number;
  name?: string;
  preferred_name: string;
  short_name?: string;
  description?: string;
  nlims_code: string;
  expected_turn_around_time: ExpectedTurnAroundTime;
  print_device?: null | string;
  created_date?: string;
  retired?: number;
  retired_reason?: null | string;
  department?: Department;
  specimens?: Specimen[];
  organisms?: any[];
  indicators?: TestIndicator[];
};

export type IndicatorRange = {
  max_age: number;
  min_age: number;
  sex: string;
  lower_range: number;
  upper_range: number;
  interpretation: string;
};

export type Autocomplete = {
  value: string;
  interpretation: string;
};

export type Numeric = {
  id?: number;
  gender: { name: string };
  max_age: number;
  min_age: number;
  upper_range: number;
  lower_range: number;
  interpretation: string;
};

export type AlphaNumeric = {
  id?: number;
  value: string;
  interpretation: string;
};

export type Indicator = {
  [x: string]: any;
  name: string;
  preferred_name?: string;
  test_indicator_type: number;
  type: {
    id?: number;
    name: string;
  };
  unit: string;
  value: string;
  id: string;
  description: string;
  indicator_ranges: Array<any>;
};

export type TestIndicatorType = {
  id: number;
  name: string;
};

/***
 * @type for drugs
 */
export type Drug = {
  id?: number;
  drug_id?: number;
  preferred_name?: string;
  description?: string;
  name: string;
  short_name?: string;
  zone?: { name: string };
  interpretation?: { name: string };
};

/**
 * @type for organism
 */
export type Organism = {
  id: number;
  name: string;
  preferred_name?: string;
  description?: string;
  drugs?: Drug[];
};
/**
 * @type for test panels
 */
export type TestPanel = {
  id?: number;
  name?: string;
  preferred_name?: string;
  short_name?: string;
  description: string;
  test_types?: Array<TestType>;
};

/****
 * @type for patient
 */
export type Patient = {
  value: any;
  id: number;
  source?: string;
  client_id: number;
  first_name: string;
  middle_name: string;
  last_name: string;
  sex: string;
  uuid: string;
  npid: string;
  gender: string;
  phone: string;
  date_of_birth: string;
  age?: number;
  birth_date_estimated: boolean;
  name?: string;
  physical_address?: string;
  current_village?: string;
  current_district?: string;
  current_traditional_authority?: string;
  home_village?: string;
  home_traditional_authority?: string;
  home_district?: string;
  created_at?: string;
};

export interface VisitType {
  id: number;
  name: string;
  description: string;
  creator: number;
  voided: number;
  voided_by?: number;
  voided_reason?: string;
  voided_date?: string;
  created_date: string;
  updated_by?: number;
  updated_date: string;
}

/**
 * @type for instrument
 */
export interface IEquipmentDetails {
  name: string;
  description: string;
  ip_address?: string;
  hostname?: string;
  supported_tests?: string | null | undefined;
  canPerform?: string | null | undefined;
  created_date?: string;
}

/**
 * @type test for a single test
 */

type Status = {
  id: number;
  name: string;
};

export type StatusTrail = {
  id: number;
  test_id: number;
  status_id: number;
  status_reason_id: number | null;
  creator: number;
  status: Status;
  status_reason: StatusReason;
  initiator: string;
  created_date: string;
};

export type Client = {
  [x: string]: any;
  id: number;
  client_id: number;
  first_name: string;
  middle_name: string;
  last_name: string;
  sex: string;
  date_of_birth: string;
  birth_date_estimated: boolean;
};

export type StatusReason = {
  id: number;
  name: string;
  description: string;
  creator: number;
  voided: number;
  voided_by: number | null;
  voided_reason: string | null;
  voided_date: string | null;
  created_date: string;
  updated_by: number | null;
  updated_date: string | null;
};

export type Test = {
  id: number;
  specimen_id: number;
  order_id: number;
  test_type_id: number;
  test_panel_id: number | null;
  voided: number;
  voided_by: string | null;
  voided_reason: string | null;
  voided_date: string | null;
  creator: number;
  created_date: string;
  updated_date: string;
  request_origin: string;
  requesting_ward: string;
  registered_by: string;
  specimen_type: string;
  accession_number: string;
  tracking_number: string;
  requested_by: string | null;
  specimen_preferred_name: string;
  test_type_name: string;
  test_type_preferred_name: string;
  test_panel_preferred_name: string;
  indicators: Indicator;
  result_remarks: { value: string };
  client: Client;
  status: string;
  rejection_reason?: string;
  order_status: string;
  status_trail: StatusTrail[];
  indicators: Indicator[];
  culture_observation: any[];
  post_crossmatch_process: {
    id: number;
    facility_section_id: number;
    test_id: number;
    collected_by: string;
    collection_date: string;
    transfusion_outcome: string;
    returned: boolean
    returned_by: string;
    returned_date: string;
    returned_reason: string;
    voided: number;
    voided_by: string;
    voided_reason: string;
    voided_date: string;
    creator: number;
    created_date: string;
    updated_date: string;
    updated_by: number;
  }
};

/**
 * type for ward
 */
export interface Ward {
  id: string;
  name: string;
  description: string;
}

/**
 * @type for surveillance
 */
export interface ISurveillanceItems {
  test_types_id: string;
  diseases_id: string;
}

export interface IDisease {
  name: string;
}
export interface Encounter {
  id: number;
  name: string;
  facility_sections: {
    id: number;
    name: string;
  };
}

interface OrderStatus {
  id: number;
  name: string;
}
interface Initiator {
  username: string;
  first_name: string;
  last_name: string;
}

interface OrderStatusTrail {
  id: number;
  order_id: number;
  status_id: number;
  status_reason_id: number | null;
  creator: number;
  status: OrderStatus;
  initiator: Initiator;
  statuses_reason: {
    id: number;
    name: string;
  };
}

export interface ClientIdentifier {
  id: number;
  client_identifier_type_id: number;
  value: string;
  client_id: number;
  voided: number;
  voided_by: string | null;
  voided_reason: string | null;
  voided_date: string | null;
  creator: number;
  created_date: string;
  updated_date: string;
  identifier_type: string;
}

interface TestIndicatorRange {
  id: number;
  test_indicator_id: number;
  min_age: number;
  max_age: number;
  sex: string;
  lower_range: string;
  upper_range: string;
  interpretation: string;
  value: string | null;
  retired: number;
  retired_by: string | null;
  retired_reason: string | null;
  retired_date: string | null;
  creator: number;
  created_date: string;
  updated_date: string;
}

interface TestIndicator {
  id: number;
  name: string;
  nlims_code: string;
  preferred_name?: string;
  test_indicator_type: string;
  result: Object;
  indicator_ranges: TestIndicatorRange[];
}

export interface OrderTest {
  id: number;
  specimen_id: number;
  order_id: number;
  test_type_id: number;
  test_panel_id: number;
  voided: number;
  voided_by: number | null;
  voided_reason: string | null;
  voided_date: string | null;
  creator: number;
  created_date: string;
  updated_date: string;
  updated_by: number;
  test_panel_name: string;
  request_origin: string;
  requesting_ward: string;
  specimen_type: string;
  accession_number: string;
  completed_by: {
    id: number;
    username: string;
    is_super_admin: boolean;
    status_id: number;
  };
  tracking_number: string;
  requested_by: string;
  test_type_name: string;
  client: {
    id: number;
    first_name: string;
    middle_name: string;
    last_name: string;
    sex: string;
    date_of_birth: string;
    birth_date_estimated: boolean;
  };
  status: string;
  order_status: string;
  indicators: TestIndicator[];
  culture_observation: any[];
  expected_turn_around_time: {
    id: number;
    test_type_id: number;
    value: string;
    unit: string | null;
    voided: number;
    voided_by: number | null;
    voided_reason: string | null;
    voided_date: string | null;
    creator: number;
    created_date: string;
    updated_date: string;
    updated_by: number;
  };
  suscept_test_result: SusceptTestResult[];
  status_trail: StatusTrail[];
  is_machine_oriented: boolean;
  order_status_trail: OrderStatusTrail[];
  registered_by: string;
}

interface SusceptTestResult {
  test_id: number;
  organism_id: number;
  name: string;
  drugs: Drug[];
}

interface OrderStatus {
  id: number;
  name: string;
}
export interface Order {
  id: number;
  [x: string]: any;
  department: any;
  status: any;
  initiator: any;
  name: any;
  test_indicator_type: string;
  id: number;
  encounter_id: number;
  priority_id: number;
  accession_number: string;
  tracking_number: string;
  requested_by: string;
  sample_collected_time: string;
  collected_by: string;
  creator: number;
  voided: number;
  voided_by: string | null;
  voided_reason: string | null;
  voided_date: string | null;
  created_date: string;
  updated_date: string;
  specimen: string;
  test_types: {
    id: number;
    name: string;
    department: string;
    print_device: boolean;
  }[];
  order_status: string;
  order_status_trail: OrderStatusTrail[];
  request_origin: string;
  requesting_ward: string;
  tests: OrderTest[];
}

export type ReportData = {
  indicator: string;
  jan: { count: string; associated_ids: string };
  feb: { count: string; associated_ids: string };
  mar: { count: string; associated_ids: string };
  totalQ1: { count: string; associated_ids: string };
  apr: { count: string; associated_ids: string };
  may: { count: string; associated_ids: string };
  june: { count: string; associated_ids: string };
  totalQ2: { count: string; associated_ids: string };
  jul: { count: string; associated_ids: string };
  aug: { count: string; associated_ids: string };
  sept: { count: string; associated_ids: string };
  totalQ3: { count: string; associated_ids: string };
  oct: { count: string; associated_ids: string };
  nov: { count: string; associated_ids: string };
  dec: { count: string; associated_ids: string };
  totalQ4: { count: string; associated_ids: string };
  total: { count: string; associated_ids: string };
};

export type StatusCount = {
  [status: string]: number;
};

export type TestSummary = {
  tests_count: { data: number };
  statuses_count: StatusCount;
};

export type Statuses =
  | "pending"
  | "completed"
  | "verified"
  | "rejected"
  | "started"
  | "not-received"
  | "not-done"
  | "specimen-not-collected"
  | "specimen-accepted"
  | "specimen-rejected";

type Datasets = {
  backgroundColor: Array<string>;
  data: Array<number>;
};

export type PieData = {
  labels: Array<string>;
  datasets: Array<Datasets>;
};
export type MReportSummary = {
  total_tested: {
    micro_over_5: { count: number; associated_ids: string };
    micro_under_5: { count: number; associated_ids: string };
    mrdt_over_5: { count: number; associated_ids: string };
    mrdt_under_5: { count: number; associated_ids: string };
  };
  total_positive: {
    micro_over_5: { count: number; associated_ids: string };
    micro_under_5: { count: number; associated_ids: string };
    mrdt_over_5: { count: number; associated_ids: string };
    mrdt_under_5: { count: number; associated_ids: string };
  };
  total_negative: {
    micro_over_5: { count: number; associated_ids: string };
    micro_under_5: { count: number; associated_ids: string };
    mrdt_over_5: { count: number; associated_ids: string };
    mrdt_under_5: { count: number; associated_ids: string };
  };
  total_male: {
    micro_over_5: { count: number; associated_ids: string };
    micro_under_5: { count: number; associated_ids: string };
    mrdt_over_5: { count: number; associated_ids: string };
    mrdt_under_5: { count: number; associated_ids: string };
  };
  total_female: {
    micro_over_5: { count: number; associated_ids: string };
    micro_under_5: { count: number; associated_ids: string };
    mrdt_over_5: { count: number; associated_ids: string };
    mrdt_under_5: { count: number; associated_ids: string };
  };
  total_in_patient: {
    micro_over_5: { count: number; associated_ids: string };
    micro_under_5: { count: number; associated_ids: string };
    mrdt_over_5: { count: number; associated_ids: string };
    mrdt_under_5: { count: number; associated_ids: string };
  };
  total_out_patient: {
    micro_over_5: { count: number; associated_ids: string };
    micro_under_5: { count: number; associated_ids: string };
    mrdt_over_5: { count: number; associated_ids: string };
    mrdt_under_5: { count: number; associated_ids: string };
  };
  total_referal: {
    micro_over_5: { count: number; associated_ids: string };
    micro_under_5: { count: number; associated_ids: string };
    mrdt_over_5: { count: number; associated_ids: string };
    mrdt_under_5: { count: number; associated_ids: string };
  };
  total_female_preg: {
    micro_over_5: { count: number; associated_ids: string };
    micro_under_5: { count: number; associated_ids: string };
    mrdt_over_5: { count: number; associated_ids: string };
    mrdt_under_5: { count: number; associated_ids: string };
  };
};

export type RequisitionItem = {
  id: number;
  stock_item: { name: string; id: number, quantity?: number };
  quantity_requested: number;
  batch_number: string;
  lot_number: string;
  expiry_date?: string;
};

export type StockLocation = {
  name: string;
  description: string;
};

export type StockSupplier = {
  id: number;
  name: string;
  description: string;
};

interface StockItem {
  id: number;
  stock_category_id: number;
  name: string;
  description: string;
  measurement_unit: number;
  quantity_unit: number;
  voided: number;
  voided_by: number | null;
  voided_reason: string | null;
  voided_date: string | null;
  creator: number;
  created_date: string;
  updated_date: string;
  updated_by: number;
  strength: string | null;
  product_code: string;
  stock: Stock;
  stock_unit?: string;
  stock_category?: string;
  stock_location?: string;
}

export interface  StockItem {
  id: number;
  stock_category_id: number;
  name: string;
  description: string;
  measurement_unit?: number;
  quantity_unit?: number;
  voided: number;
  voided_by: number | null;
  voided_reason: string | null;
  voided_date: string | null;
  creator: number;
  created_date: string;
  updated_date: string;
  updated_by: number;
  strength: string | null;
  product_code?: string;
  stock_location_id: number;
  minimum_order_level: number;
  quantity: number;
  batches?: string[];
  lots?: string[];
}

export interface Stock {
  id: number;
  stock_item_id: number;
  stock_location_id: number;
  quantity: number;
  voided: number;
  voided_by: number | null;
  voided_reason: string | null;
  voided_date: string | null;
  creator: number;
  created_date: string;
  updated_date: string;
  updated_by: number;
  minimum_order_level: number;
  stock_item?: StockItem;
  stock_location?: StockLocation;
}


export type StockTransactionType = {
  id: number;
  name: string;
};

export type StockOrder = {
  voucher_number: number;
  requisitions: Array<{
    stock_item_id: number;
    quantity_requested: number;
  }>;
};

export type Facility = {
  id: number;
  name: string;
  code: string;
  address: string;
  phone: string;
  district: string;
};

export type Gender = {
  name: string;
  label: string;
};

export type TimeDuration = {
  name: string;
};

export type TestActionButton = {
  text: string;
  color: string;
  status: string;
  icon: Object;
  show: string;
  permission?: string;
};

export type RejectStatus = {
  name: string;
  icon: Object;
  action: string;
  permission?: string;
};

export type TableField = {
  label: string;
  value: boolean;
  property: string;
  table_value: string;
};

export type DrillDownReportItem = {
  sn: number;
  patient_id: number;
  patient_name: string;
  accession_number: string;
  test_type_name: string;
  specimen_type: string;
  result: string;
  result_date: string;
  completed: { username: string };
  requested_by: string;
  tracking_number: string;
  lab_location: string;
  date_registered: string;
  rejection_reason: string;
};

export type Month =
  | "January"
  | "February"
  | "March"
  | "April"
  | "May"
  | "June"
  | "July"
  | "August"
  | "September"
  | "October"
  | "November"
  | "December";

export type TBREPORTDATA = {
  TEST: string;
  RESULT: string;
};

export type InfectionReportDefinition = "L_E_5" | "G_5_L_E_14" | "G_14";
export type ReportDrilldownOrigin = "aggregate" | "culture" | "moh" | "daily";

export type InfectionReportSummary = {
  id: number;
  name: string;
  associated_ids: string;
  total?: number;
};
