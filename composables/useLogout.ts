import { useRouteStore } from "@/store/route";
import { useAuthStore } from "@/store/auth"

export default function useLogout () {
    const { logUserOut } = useAuthStore();
    const { lastKnownRoute } = useRouteStore();
    const route = useRoute();

    // Capture current route before logout
    if (route.path && route.path !== '/' && route.path !== '/login') {
        lastKnownRoute(route.path);
    } else {
        lastKnownRoute('/home');
    }

    logUserOut();
}
