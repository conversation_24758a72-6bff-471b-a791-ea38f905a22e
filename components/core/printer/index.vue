<template>
  <div>
    <CoreActionButton :click="init" color="primary" text="Print" :icon="printIcon" />

    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex top-0 items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all"
              >
                <div class="border-b px-3 py-3 flex items-center justify-between">
                  <DialogTitle
                    as="h3"
                    class="text-lg flex items-center font-medium leading-6"
                  >
                    <PrinterIcon class="h-5 w-5 mr-2" />
                    Print
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <FormKit
                  type="form"
                  submit-label="Update"
                  @submit="submitForm"
                  :actions="false"
                  #default="{ value }"
                >
                  <div class="py-5 px-5">
                    <FormKit
                      validation="required"
                      label="Select a printer"
                      type="radio"
                      v-model="selectedPrinter"
                      :options="printers"
                    />
                  </div>

                  <div
                    class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t"
                  >
                    <CoreActionButton
                      type="button"
                      :loading="zebraPrinting"
                      :click="
                        () => {
                          toPrintSmallLabel();
                        }
                      "
                      v-if="printSmallLabel"
                      :icon="printIcon"
                      text="Print Small Label"
                      color="primary"
                    />
                    <CoreActionButton
                      :click="() => {}"
                      :loading="loading"
                      type="submit"
                      :icon="printIcon"
                      text="Print"
                      color="success"
                    />
                  </div>
                </FormKit>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  PrinterIcon,
  ExclamationTriangleIcon,
} from "@heroicons/vue/24/solid/index.js";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response, Test } from "@/types";

export default {
  components: {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    XMarkIcon,
    ExclamationTriangleIcon,
    PrinterIcon,
  },
  props: {
    id: {
      type: String,
    },
    orderId: {
      type: String,
    },
    printSmallLabel: {
      required: false,
      type: Boolean,
      default: false,
    },
    tests: {
      type: Array<String>,
      required: false,
    },
    orderTests: {
      type: Array<Test>,
      required: false
    }
  },
  data() {
    return {
      show: false as boolean,
      printIcon: PrinterIcon as Object,
      cookie: useCookie("token"),
      printers: new Array<string>(),
      selectedPrinter: "" as string,
      loading: false as boolean,
      zebraPrinting: false as boolean,
    };
  },
  methods: {
    async init(): Promise<void> {
      this.handleClick();

      const request: Request = {
        route: endpoints.printers,
        method: "GET",
        token: `${this.cookie}`,
      };

      const { data, error }: Response = await fetchRequest(request);

      if (data.value) {
        this.printers = new Array<string>();

        data.value.map((value: { name: string }) => {
          this.printers.push(value.name);
        });
      }

      if (error.value) {
        console.error(error.value);
      }
    },
    isCrossMatchTest() : boolean {
      return this.orderTests ? this.orderTests?.some((test) => test.test_type_name.toLowerCase() == 'cross-match') : false;
    },
    async toPrintSmallLabel(): Promise<void> {
      this.zebraPrinting = true;
      const request: Request = {
        route: endpoints.printOutZebra,
        method: "POST",
        token: `${this.cookie}`,
        body: {
          order_id: this.orderId,
          tests: this.tests,
          is_cross_match: this.isCrossMatchTest()
        },
      };

      const { data, error, pending }: Response = await fetchRequest(request);
      this.zebraPrinting = pending;

      if (data.value) {
        this.zebraPrinting = false;
        const reader = new FileReader();
        reader.onload = () => {
          const url = URL.createObjectURL(data.value);

          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", `${Date.now()}.lbl`);
          link.click();

          URL.revokeObjectURL(url);
        };

        reader.readAsText(data.value);
      }

      if (error.value) {
        this.zebraPrinting = false;
        console.error(error.value);
      }
    },
    containsWords(style: { cssText: string | any[] }, ...words: any[]) {
      for (let i = 0; i < words.length; i++) {
        if (style.cssText.includes(words[i])) {
          return true;
        }
      }
      return false;
    },
    async submitForm(file: Blob): Promise<void> {
      let formData = new FormData();
      this.loading = true;
      let OrderId = new Array<number>();
      OrderId.push(Number(this.orderId));
      let client_id = this.id as string;

      // formData.append("pdf", file, `patient-name-${this.id}`);
      formData.append("printer_name", this.selectedPrinter);
      formData.append("order_ids", JSON.stringify(OrderId));
      formData.append("client_id", client_id);

      const request: Request = {
        route: endpoints.printOut,
        method: "POST",
        token: `${this.cookie}`,
        body: formData,
      };

      const { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;

      if (data.value) {
        if (data.value.printed)
          useNuxtApp().$toast.success("Patient report printed successfully!");
        else useNuxtApp().$toast.warning("Could not print patient report!");
        this.loading = false;
        this.$emit("update", true);
        this.$route.query.printer?.toString() == "true" && this.$router.back();
        this.handleClick();
      }

      if (error.value) {
        this.loading = false;
        console.error(error.value);
        useNuxtApp().$toast.error(
          "An error occurred while printing patient report"
        );
        this.handleClick();
        this.$route.query.printer?.toString() == "true" &&
          this.$router.push("/tests");
      }
    },
    handleClick(): void {
      this.show = !this.show;
      if(this.show == false){
        this.$router.back();
      }
    },
  },
};
</script>
<style></style>
